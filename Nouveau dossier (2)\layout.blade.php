<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
        <title>Africatalents</title>
        <link rel="icon" type="image/x-icon" href="../src/assets/img/favicon.ico"/>
       <!-- Include TinyMCE JavaScript -->

        <script src="{{asset('admin/js/loader.js')}}"></script>
        <link href="{{asset('admin/bootstrap/css/bootstrap.min.css')}}" rel="stylesheet" type="text/css" />
       

        <link href="https://fonts.googleapis.com/css?family=Nunito:400,600,700" rel="stylesheet">
        
        <link href="{{asset('admin/css/plugins.css')}}"  rel="stylesheet" type="text/css" />
        <link href="{{asset('admin/css/modal.css')}}" rel="stylesheet" type="text/css" />
        <link href="{{asset('admin/css/todolist.css')}}" rel="stylesheet" type="text/css" />
        <link href="{{asset('admin/css/mailbox.css')}}" rel="stylesheet" type="text/css" />
        <link href="{{asset('admin/css/editors/quill/quill.snow.css')}}"  type="text/css" />
        <link href="{{asset('admin/css/admin.css')}}"  type="text/css" />

        <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js" integrity="sha512-894YE6QWD5I59HgZOGReFYm4dnWc1Qt5NtvYSaNcOP+u1T9qYdvdihz0PPSiiqn/+/3e7Jo4EaG7TubfWGUrMQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>

    
    </head>
    <body>                                       
        @include('admin.includes.navbar')

            @yield('content')
            <script src="{{asset('admin/bootstrap/js/bootstrap.bundle.min.js')}}"></script>

            <script src="{{asset('admin/js/global/vendors.min.js')}}"></script>
            <script src="{{asset('admin/js/perfect-scrollbar/perfect-scrollbar.min.js')}}"></script>
            <script src="{{asset('admin/js/mousetrap/mousetrap.min.js')}}"></script>
            <script src="{{asset('admin/js/waves/waves.min.js')}}"></script>
            
            <script src="{{asset('admin/js/app.js')}}"></script>
            <script src="{{asset('admin/js/editors/quill/quill.js')}}"></script>
            <script src="{{asset('admin/js/todoList.js')}}"></script>
            <script src="{{asset('admin/js/mailbox.js')}}"></script>
            
    </body>
</html>   

<script type="text/javascript">
    $(document).ready(function() {




        $('#sendEmailButton').on('click', function(e) {
            e.preventDefault(); 
           
            let formData = new FormData($('#variablesForm')[0]);
    
            $.ajax({
                url: '/modifier-mission', 
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {

                    alert('Vos variables modifiées avec succès dans la table DeposerMission !');

                },
                error: function(xhr, status, error) {
                    // Gestion des erreurs
                    alert('Une erreur s\'est produite: ' + error);
                }
            });
        });



    $('.nav-link.list-actions').on('click', function(e) {
     e.preventDefault(); 

        $('.nav-link.list-actions').removeClass('active');

        $(this).addClass('active');

        var filter = $(this).data('filter');

        var url = window.location.href.split('?')[0]; 
        window.location.href = url + '?filter=' + filter;
    });

    var urlParams = new URLSearchParams(window.location.search);
    var filterParam = urlParams.get('filter');

  if (filterParam) {
    $('.nav-link.list-actions').removeClass('active'); 
    $('.nav-link.list-actions[data-filter="' + filterParam + '"]').addClass('active'); 
  } else {
    $('#all-list').addClass('active');
  }

            
    });
    </script>

<script>



    function handleMissionAction(action,mission) {
      document.getElementById('recipientEmail'+ action).value = mission.email;
      document.getElementById('id_mission').value = mission.id;
      document.getElementById('nom').value = mission.nom;
      document.getElementById('prenom').value = mission.prenom;
      document.getElementById('entreprise').value = mission.nom_entreperise;
      document.getElementById('telephone').value = mission.telephone;
      document.getElementById('civilitee').value = mission.civilitee;
      document.getElementById('modal_prestation').textContent = mission.prestation;
      document.getElementById('modal_decription').textContent = mission.description;

    }
  
    function confirmSubmission() 
    {
        return confirm("Êtes-vous sûr de vouloir envoyer ce mail ?");
    }
  
  
  
  
    let currentLanguage = 'fr'; // Garde une trace de la langue actuelle

// Fonction pour traduire les éléments statiques basés sur les data-attributes
function translateStaticElements(language) {
    currentLanguage = language; // Met à jour la langue actuelle
    const modal = document.getElementById('accepter'); // Cible uniquement dans le modal

    // Traduit les éléments avec data-translate-fr/en (Labels, boutons simples, etc.)
    modal.querySelectorAll('[data-translate-fr]').forEach(el => {
        const translation = el.getAttribute('data-translate-' + language);
        if (translation) {
             // Gère différents types d'éléments si nécessaire (ex: options de select)
             if (el.tagName === 'OPTION') {
                 // Ne change pas la valeur, seulement le texte affiché
                 if(!el.disabled) { // Ne pas traduire les options disabled comme "Mail template" si elles n'ont pas de traduction
                      // Optionnel : si tu veux aussi traduire le texte de l'option sélectionnée par défaut
                 } else if (el.disabled && el.getAttribute('data-translate-' + language)) {
                      el.textContent = translation;
                 }
             } else if (el.id !== 'toggleButton') { // Exclut le bouton toggle pour gestion spéciale
                el.textContent = translation;
             }
        }
    });
    // Traduit les placeholders
    modal.querySelectorAll('[data-translate-fr-placeholder]').forEach(input => {
        const placeholderTranslation = input.getAttribute('data-translate-' + language + '-placeholder');
        if (placeholderTranslation) {
            input.placeholder = placeholderTranslation;
        }
    });

    // Met à jour le texte du bouton Prévisualiser s'il existe
    const previewButton = modal.querySelector('button.btn-secondary[onclick="previewEmail()"]');
     if (previewButton) {
        previewButton.textContent = (language === 'fr') ? 'Prévisualiser' : 'Preview';
     }

    // Met à jour le texte du bouton Toggle spécifiquement
    updateToggleButtonText(language);

}

// Fonction pour mettre à jour le texte du bouton toggle
function updateToggleButtonText(language) {
    const toggleButton = document.getElementById('toggleButton');
    const textarea = document.getElementById('emailMessage');
    if (toggleButton && textarea) {
        const isReadOnly = textarea.readOnly;
        // Clés basées sur l'état readonly
        const attrKey = isReadOnly ? 'manual' : 'vars';
        const text = toggleButton.getAttribute(`data-translate-${language}-${attrKey}`);
        toggleButton.textContent = text || ''; // Assure une valeur par défaut
    }
}





function translateCivilite(civilite, langue) {
    // Gère le cas où civilite est null ou undefined
    if (!civilite) {
        return ''; // Ou retourne ce qui est approprié dans ton cas
    }

    civilite = civilite.toLowerCase(); // Convertit en minuscules pour la comparaison

    const translations = {
        'fr': {
            'male': 'Monsieur',
            'Male': 'Monsieur',
            'mr': 'Monsieur',
            'mr.': 'Monsieur',
            'Mr': 'Monsieur',
            'monsieur': 'Monsieur', // Accepte aussi l'entrée française
            'female': 'Madame',
            'Female': 'Madame',
            'mme': 'Madame',
            'madame': 'Madame',    // Accepte aussi l'entrée française
            'ms': 'Madame',        // Accepte aussi les entrées anglaises courantes
            'mrs': 'Madame',
            'miss': 'Madame',
            'M.': 'Monsieur' ,         // Standardise 'Miss' en 'Ms.'
            'm.': 'Monsieur' ,         // Standardise 'Miss' en 'Ms.'
            'Mme.': 'Madame' , 
            'mme.': 'Madame' , 
            'Mme': 'Madame' , 
            'ms.': 'Madame',
        },
        'en': { // Ajout des traductions anglaises
            'male': 'Mr.',
            'Male': 'Mr.',
            'Mr.': 'Mr.',
            'mr.': 'Mr.',
            'mr': 'Mr.',
            'monsieur': 'Mr.',     // Traduit l'entrée française en anglais si la langue est 'en'
            'female': 'Ms.',       // Utilise 'Ms.' comme standard neutre
            'Female': 'Ms.',       // Utilise 'Ms.' comme standard neutre
            'mme.': 'Ms.',          // Traduit l'entrée française
            'madame': 'Ms.',       // Traduit l'entrée française
            'ms': 'Ms.',
            'ms.': 'Ms.',
            'mrs': 'Ms.',          // Standardise 'Mrs.' en 'Ms.'
            'miss': 'Ms.' ,         // Standardise 'Miss' en 'Ms.'
            'M.': 'Mr.' ,         // Standardise 'Miss' en 'Ms.'
            'm.': 'Mr.' ,         // Standardise 'Miss' en 'Ms.'
            'Mme.': 'Ms.' ,         // Standardise 'Miss' en 'Ms.'
            'Mme': 'Ms.' ,         // Standardise 'Miss' en 'Ms.'
            'mme': 'Ms.' ,         // Standardise 'Miss' en 'Ms.'
        }
    };

  
    if (translations[langue] && translations[langue][civilite]) {
        return translations[langue][civilite];
    } else {
      
         return civilite; // Retourne la valeur en minuscules
       
    }
}




function replaceVariables2(text) {
        const variableMap = {

        '[entreprise]': document.getElementById('entreprise').value.toUpperCase(),
    };

    // Vérifier si la variable cvs existe côté serveur et la remplacer
    if (typeof window.cvs !== 'undefined' && window.cvs !== null) {
        variableMap['[cvs]'] = window.cvs;
    } else {
        // Si la variable n'existe pas côté serveur, essayer de récupérer depuis une variable globale
        if (typeof cvs !== 'undefined' && cvs !== null) {
            variableMap['[cvs]'] = cvs;
        }
    }

    console.log(variableMap)

    // Remplacer chaque variable dans le texte
    Object.keys(variableMap).forEach(key => {
        // const regex = new RegExp(key, 'g');
        // console.log(regex)
        text = text.replace(key, variableMap[key]);
        console.log(text)

    });






    var fields = document.querySelectorAll('[data-variable]');
    // if (fields.length > 0) { // Vérification s'il y a des champs
    console.log(fields)
        fields.forEach(function(field) {
            var variableName = field.getAttribute('data-variable');
            var regex = new RegExp(`\\[${variableName}\\]`, 'g');

            if (field.type === 'checkbox') {
                // Si la checkbox est décochée, laisser la variable telle qu'elle est
                text = text.replace(regex, field.checked ? field.value : `[${variableName}]`);
            } else if (field.type === 'text') {
                // Si le champ texte est vide, laisser la variable telle qu'elle est
                text = text.replace(regex, field.value.trim() ? field.value : `[${variableName}]`);
            }
            // else if (field.tagName.toLowerCase() === 'select') {
            //     // Pour les éléments select
            //     var selectedOption = field.options[field.selectedIndex];
            //     text = text.replace(regex, selectedOption.value ? selectedOption.value : `[${variableName}]`);
            // }
        });
    // } else {
    //     console.log("Aucun champ dynamique trouvé.");
    // }
    return  text;

    }











//     return text;
// }

function replaceVariables(text, variables) {
    const langue = document.getElementById('languageSelect').value

    const civilite = document.getElementById('civilitee').value;
    const translatedCivilite = translateCivilite(civilite, langue);

    const variableMap = {
        '[civilitee]': translatedCivilite,
        '[nom]': document.getElementById('nom').value,
        '[prenom]': document.getElementById('prenom').value,
        '[entreprise]': document.getElementById('entreprise').value.toUpperCase()
    };

    // Vérifier si la variable cvs existe côté serveur et la remplacer
    if (typeof window.cvs !== 'undefined' && window.cvs !== null) {
        variableMap['[cvs]'] = window.cvs;
    } else {
        // Si la variable n'existe pas côté serveur, essayer de récupérer depuis une variable globale
        if (typeof cvs !== 'undefined' && cvs !== null) {
            variableMap['[cvs]'] = cvs;
        }
    }

    const posteField = document.querySelector('[data-variable="Poste"]');
    if (posteField && posteField.value.trim() !== '') {
        const postePrefix = currentLanguage === 'fr' ? 
            " pour le poste de " : 
            " for the position of ";
        
        const regex = new RegExp('\\[Poste\\]', 'g');
        text = text.replace(regex, postePrefix + posteField.value);
    }
    
    // Traitement spécial pour le champ Pays multi-select
    const paysField = document.querySelector('[data-variable="Pays"]');
    if (paysField && paysField.multiple) {
        // Pour un select multiple, récupérer toutes les options sélectionnées
        const selectedOptions = Array.from(paysField.selectedOptions);
        if (selectedOptions.length > 0) {
            const selectedValues = selectedOptions.map(option => option.value);
            const paysText = selectedValues.join('');
            
            const regex = new RegExp('\\[Pays1\\]', 'g');
            text = text.replace(regex, paysText);
        }
    }
    
    // Remplacer chaque variable dans le texte
    Object.keys(variableMap).forEach(key => {
        text = text.replace(key, variableMap[key]);
    });

    // Traiter les champs dynamiques individuels
    var fields = document.querySelectorAll('[data-variable]:not([name$="[]"]):not([multiple])');
    fields.forEach(function(field) {
        var variableName = field.getAttribute('data-variable');
        var regex = new RegExp(`\\[${variableName}\\]`, 'g');

        if (field.type === 'checkbox') {
            text = text.replace(regex, field.checked ? field.value : `[${variableName}]`);
        } else if (field.type === 'text') {
            text = text.replace(regex, field.value.trim() ? field.value : `[${variableName}]`);
        } else if (field.tagName.toLowerCase() === 'select' && !field.multiple) {
            var selectedOption = field.options[field.selectedIndex];
            text = text.replace(regex, selectedOption.value ? selectedOption.value : `[${variableName}]`);
        }
    });


    document.querySelectorAll('select[multiple][data-variable]').forEach(function(select) {
    var variableName = select.getAttribute('data-variable');
    var regex = new RegExp(`\\[${variableName}\\]`, 'g');

    var selectedOptions = Array.from(select.selectedOptions);

    if (selectedOptions.length === 0) {
        text = text.replace(regex, `[${variableName}]`);
        return;
    }

    // Générer les pays avec articles selon la langue
    let formatted = '';
    const langue = document.getElementById('languageSelect').value

    if (langue === 'fr') {
        let countriesWithArticles = selectedOptions.map(option => {
            let article = option.getAttribute('data-article') || '';
            let label = option.textContent.trim();
            return article + (article && article !== "l'" ? ' ' : '') + label;
        });

        if (countriesWithArticles.length === 1) {
            formatted = ', dont ' + countriesWithArticles[0]+' '; // ou 'au', à adapter selon article
        } else {
            formatted = ', dont ' + countriesWithArticles.slice(0, -1).join(', ') + ' et ' + countriesWithArticles.slice(-1)+' ';
        }

    } else if (langue === 'en') {
        let countryNames = selectedOptions.map(option => option.textContent.trim());

        if (countryNames.length === 1) {
            formatted = ', including ' + countryNames[0]+' ';
        } else {
            formatted = ', including ' + countryNames.slice(0, -1).join(', ') + ' and ' + countryNames.slice(-1)+' ';
        }
    }

    // Remplacer dans le texte principal
    text = text.replace(regex, formatted);
});


    // Traiter les checkbox-groups
    var checkboxGroups = document.querySelectorAll('.checkbox-group-container');
    checkboxGroups.forEach(function(container) {
        var variableName = container.getAttribute('data-variable');
        var regex = new RegExp(`\\[${variableName}\\]`, 'g');
        
        var checkedBoxes = container.querySelectorAll('input[type="checkbox"]:checked');
        var values = [];
        
        checkedBoxes.forEach(function(checkbox) {
            values.push(checkbox.value);
        });
        
        text = text.replace(regex, values.length > 0 ? values.join('\n') : `[${variableName}]`);
    });

    return replaceVariables2(text);
}


function replaceSubject(text) {


    const variableMap = {
        '[entreprise]': document.getElementById('entreprise').value.toUpperCase(),
        // '[poste]': document.getElementByName('poste').value,
    };
    
    Object.keys(variableMap).forEach(key => {
      
        text = text.replace(key, variableMap[key]);

    });







    var fields = document.querySelectorAll('[data-variable]');
    // if (fields.length > 0) { // Vérification s'il y a des champs
    console.log(fields)
        fields.forEach(function(field) {
            var variableName = field.getAttribute('data-variable');
            var regex = new RegExp(`\\[${variableName}\\]`, 'g');

            if (field.type === 'checkbox') {
                // Si la checkbox est décochée, laisser la variable telle qu'elle est
                text = text.replace(regex, field.checked ? field.value : `[${variableName}]`);
            } else if (field.type === 'text') {
                // Si le champ texte est vide, laisser la variable telle qu'elle est
                text = text.replace(regex, field.value.trim() ? field.value : `[${variableName}]`);
            }
            // else if (field.tagName.toLowerCase() === 'select') {
            //     // Pour les éléments select
            //     var selectedOption = field.options[field.selectedIndex];
            //     text = text.replace(regex, selectedOption.value ? selectedOption.value : `[${variableName}]`);
            // }
        });

    return text;
}






function updateEmailContent() {
    var selectElement = document.getElementById('mailTemplateSelect');
    var selectedOption = selectElement.options[selectElement.selectedIndex];
    var language = document.getElementById('languageSelect').value;

    var subject, message, variables;

    if (language === 'fr') {
        subject = selectedOption.getAttribute('data-subject-fr');
        message = selectedOption.getAttribute('data-message-fr');
        variables = selectedOption.getAttribute('data-variables_fr');
    } else if (language === 'en') {
        subject = selectedOption.getAttribute('data-subject-en');
        message = selectedOption.getAttribute('data-message-en');
        variables = selectedOption.getAttribute('data-variables_en');
        console.log('test1 ',subject)
    }

    // Fournir des valeurs par défaut si les attributs sont manquants
    subject = subject || '';
    message = message || '';
    console.log('test2 ',subject)

    var emailMessageEl = document.getElementById('emailMessage');
    var emailSubjectEl = document.getElementById('emailSubject');

    // 1. Stocker les templates originaux D'ABORD
    emailMessageEl.setAttribute('data-original-message', message);
    emailSubjectEl.setAttribute('data-original-subject', subject); // Utiliser le nouvel attribut

    // 2. Initialiser les champs avec les templates BRUTS (sans remplacement initial)
    // Le remplacement se fera à l'étape 4
    emailMessageEl.value = message;
    emailSubjectEl.value = subject;
    console.log('test3 ',emailSubjectEl.value)

    // 3. Afficher les champs dynamiques (ceux-ci peuvent avoir des variables dans leurs valeurs/labels)
    displayDynamicFields(variables);

    // 4. *** APPELER LES FONCTIONS DE REMPLACEMENT APRÈS displayDynamicFields ***
    //    Celles-ci liront les attributs 'data-original-*' et remplaceront en utilisant
    //    les valeurs actuelles de TOUS les champs (statiques + dynamiques nouvellement créés).
    replaceVariablesOnUserInput();
    replaceSubjectOnUserInput();

    // 5. Traduire les éléments statiques (labels, boutons, etc.)
    translateStaticElements(language);
}





function displayDynamicFields(variables) {
    var container = document.getElementById('dynamicFieldsContainer');
    container.innerHTML = ''; 

    if (variables) {
        var variablesArray = JSON.parse(variables); 
       
        variablesArray.forEach(function(variable) {
            var fieldWrapper = document.createElement('div');
            fieldWrapper.className = 'mb-3 d-flex justify-content-between align-items-center';

            // Ajouter la condition pour le champ references
            if (variable.name === 'references') {
                fieldWrapper.style.display = 'none'; // Masquer par défaut
                fieldWrapper.setAttribute('data-conditional-field', 'references');
            }

            var label = document.createElement('label');
            label.className = 'form-label';
            label.textContent = variable.label;
            fieldWrapper.appendChild(label);

            var field;

            if (variable.type === 'input') {
                field = document.createElement('input');
                field.type = 'text';
                field.className = 'form-control input';
                field.name = variable.name;
                field.placeholder = variable.label;
                field.required = !variable.optionnel; 
                if (variable.value) {
                    field.value = variable.value;
                }
                
                field.setAttribute('data-variable', variable.name);
                fieldWrapper.appendChild(field);
                
            } else if (variable.type === 'checkbox') {
                fieldWrapper.className += ' form-check';

                field = document.createElement('input');
                field.type = 'checkbox';
                field.className = 'form-check-input';
                field.name = variable.name;
                field.id = variable.name;
                field.required = !variable.optionnel;
                field.value = variable.value;

                field.setAttribute('data-variable', variable.name);
                
                label.className = 'form-check-label';
                label.htmlFor = variable.name;
                if(variable.name=="echange_telephonique"){
                    label.textContent = variable.label;
                }else{
                    label.textContent = variable.value;
                }
                
                fieldWrapper.appendChild(field);
                
            } else if (variable.type === 'multi-select') {
                // Nouveau type : multi-select
                field = document.createElement('select');
                field.className = 'form-select input';
                field.name = variable.name + '[]'; // Notation array pour multiple valeurs
                field.multiple = true; // Permet la sélection multiple
                field.required = !variable.optionnel;
                field.setAttribute('data-variable', variable.name);
                field.style.height = '120px'; // Hauteur fixe pour voir plusieurs options

                if (variable.options && Array.isArray(variable.options)) {
                    variable.options.forEach(function(option) {
                        var optionElement = document.createElement('option');
                        optionElement.value = option.value;
                        optionElement.textContent = option.label;

                        if (option.article) {
                            optionElement.setAttribute('data-article', option.article);
                        }
                        // Vérifier si cette option est présélectionnée
                        if (variable.value && Array.isArray(variable.value) && 
                            variable.value.includes(option.value)) {
                            optionElement.selected = true;
                        }
                        
                        field.appendChild(optionElement);
                    });
                }
                
                fieldWrapper.appendChild(field);
                
            } else if (variable.type === 'checkbox-group') {
                // Pour le type checkbox-group, créer un conteneur pour les checkboxes
                fieldWrapper.className = 'mb-3';
                label.className = 'form-label d-block';
                
                // Créer un div pour contenir toutes les checkboxes
                var checkboxContainer = document.createElement('div');
                checkboxContainer.className = 'checkbox-group-container ms-5';
                checkboxContainer.setAttribute('data-variable', variable.name);
                
                if (variable.options && Array.isArray(variable.options)) {
                    variable.options.forEach(function(option, index) {
                        var checkWrapper = document.createElement('div');
                        checkWrapper.className = 'form-check mb-2';
                        
                        var checkInput = document.createElement('input');
                        checkInput.type = 'checkbox';
                        checkInput.className = 'form-check-input';
                        checkInput.name = variable.name + '[]';
                        checkInput.id = variable.name + '_' + index;
                        checkInput.value = option.value;
                        checkInput.setAttribute('data-variable', variable.name);
                        
                        if (variable.value && Array.isArray(variable.value) && 
                            variable.value.includes(option.value)) {
                            checkInput.checked = true;
                        }
                        
                        var checkLabel = document.createElement('label');
                        checkLabel.className = 'form-check-label';
                        checkLabel.htmlFor = variable.name + '_' + index;
                        checkLabel.textContent = option.label;
                        
                        checkWrapper.appendChild(checkInput);
                        checkWrapper.appendChild(checkLabel);
                        checkboxContainer.appendChild(checkWrapper);
                    });
                }
                
                fieldWrapper.appendChild(checkboxContainer);
                
            } else if (variable.type === 'select') {
                field = document.createElement('select');
                field.className = 'form-select input';
                field.name = variable.name;
                field.required = !variable.optionnel;
                field.setAttribute('data-variable', variable.name);

                if (variable.options && Array.isArray(variable.options)) {
                    variable.options.forEach(function(option) {
                        var optionElement = document.createElement('option');
                        optionElement.value = option.value;
                        optionElement.textContent = option.label;
                        if (variable.value && variable.value === option.value) {
                            optionElement.selected = true;
                        }
                        if (variable.name === 'site' && option.devise) {
                            optionElement.setAttribute('data-devise', option.devise);
                        }
                        field.appendChild(optionElement);
                    });
                }
                
                fieldWrapper.appendChild(field);
            }
            
            container.appendChild(fieldWrapper);
        });
    }
    addDynamicFieldListeners();
    addDynamicsubjectListeners();
}




function replaceVariablesOnUserInput() {
    var emailMessage = document.getElementById('emailMessage');
    var originalMessage = emailMessage.getAttribute('data-original-message') || emailMessage.value;


    if (!emailMessage.hasAttribute('data-original-message')) {
        emailMessage.setAttribute('data-original-message', originalMessage);
    }

    var updatedMessage = replaceVariables(originalMessage);
    emailMessage.value = updatedMessage;
}



function replaceSubjectOnUserInput() {
    var emailSubject = document.getElementById('emailSubject');
    var originalMessage = emailSubject.getAttribute('data-original-subject') || emailSubject.value;

    if (!emailSubject.hasAttribute('data-original-subject')) {
        emailSubject.setAttribute('data-original-subject', originalMessage);
        console.log('test5',emailSubject);

    }

    var updatedMessage = replaceSubject(originalMessage);
    emailSubject.value = updatedMessage;
}



function addDynamicFieldListeners() {
    var fields = document.querySelectorAll('[data-variable]');
    
    fields.forEach(function(field) {
        field.addEventListener('change', function(event) {
            field.classList.remove('error-highlight');
            
            if (field.name === 'site') {
                var selectedOption = field.options[field.selectedIndex];
                var devise = selectedOption.getAttribute('data-devise');
                var deviseField = document.querySelector('select[name="devise"]');
                if (deviseField && devise) {
                    deviseField.value = devise;
                    var changeEvent = new Event('change');
                    deviseField.dispatchEvent(changeEvent);
                }
            }
            
            replaceVariablesOnUserInput(event);
            replaceSubjectOnUserInput();
        });
    });
    
    // Ajouter des écouteurs pour les checkbox groups
    var checkboxGroups = document.querySelectorAll('.checkbox-group-container input[type="checkbox"]');
    checkboxGroups.forEach(function(checkbox) {
        checkbox.addEventListener('change', function() {
            // Vérifier si c'est l'option "Nos références"
            if (checkbox.value === "Nous accompagnons déjà des sociétés telles que [references].\n") {
                var referencesField = document.querySelector('[data-conditional-field="references"]');
                if (referencesField) {
                    referencesField.style.display = checkbox.checked ? 'flex' : 'none';
                }
            }

            replaceVariablesOnUserInput();
            replaceSubjectOnUserInput();
        });
    });
    
    // Ajouter des écouteurs spécifiques pour les multi-select
    var multiSelects = document.querySelectorAll('select[multiple][data-variable]');
    multiSelects.forEach(function(select) {
        select.addEventListener('change', function() {
            replaceVariablesOnUserInput();
            replaceSubjectOnUserInput();
        });
    });
}





function addDynamicsubjectListeners() {
    var field = document.getElementById('entreprise');
        field.addEventListener('change', replaceSubjectOnUserInput); 
    
}



// document.getElementById('entreprise').addEventListener('change', replaceVariablesOnUserInput);
document.getElementById('mailTemplateSelect').addEventListener('change', updateEmailContent);
document.getElementById('languageSelect').addEventListener('change', updateEmailContent);

  
function handleMissionEtat(action, mission) {
    console.log('test')
    if (action === "refuser") {
        $.ajax({
            url: '/modifier-etat-mission',
            type: 'POST',
            data: {
                _token: '{{ csrf_token() }}',
                etat: 'refuser',
                id_mission: mission.id
            },
            success: function(response) {
                alert(response.success);
                location.reload();
            },
            error: function(xhr) {
                alert('Une erreur s\'est produite lors de la modification de la mission.');
            }
        });
    }
}




setTimeout(function() {
        let flashMessage = document.getElementById('flash-message');
        if (flashMessage) {
            flashMessage.style.display = 'none';
        }
    }, 3000);





    function updateFieldsState()
     {

                const fields = document.querySelectorAll('[data-variable]');

                // Sélectionnez le textarea, s'il existe
                const textarea = document.getElementById('emailMessage');

                // Sélectionnez le bouton de bascule
                const toggleButton = document.getElementById('toggleButton');

                // État initial
                let isTextareaEditable = textarea.readOnly;


                if (!isTextareaEditable) {
                    const confirmMessage = "Si vous activez cette option, vous perdrez toutes les modifications manuelles que vous avez effectuées. Voulez-vous continuer ?";
                    const userConfirmed = confirm(confirmMessage);

                    // Si l'utilisateur annule, arrêter la fonction
                    if (!userConfirmed) {
                        return;
                    }
                }


                
           console.log(textarea.readOnly);
           textarea.readOnly = !isTextareaEditable;
           toggleButton.textContent = textarea.readOnly ? 'activer la modification manuelle' : 'activer les variables';
        

                fields.forEach(field => {
                if (field.type === 'checkbox') {
                    field.disabled = isTextareaEditable;
                } else {
                    field.readOnly = isTextareaEditable;
                }
            });

            updateToggleButtonText(currentLanguage);

    }

  
    document.getElementById('languageSelect').removeEventListener('change', updateEmailContent); // Retire l'ancien au cas où
document.getElementById('languageSelect').addEventListener('change', updateEmailContent);

// Appelle la traduction initiale lorsque le modal est montré
var emailModal = document.getElementById('accepter');
emailModal.addEventListener('show.bs.modal', function (event) {
    // Assure que le contenu est prêt et que la langue par défaut est appliquée
    // updateEmailContent est probablement déjà appelé par handleMissionAction indirectement
    // mais on force la traduction statique pour la langue par défaut au cas où.
     setTimeout(() => { // Léger délai pour s'assurer que le contenu est là
        const initialLanguage = document.getElementById('languageSelect').value || 'fr';
        translateStaticElements(initialLanguage);
        // Assure-toi que le bouton toggle a le bon texte initial
        updateToggleButtonText(initialLanguage);
    }, 100); // Ajuste si nécessaire
});



  function previewEmail() {


    const form = document.getElementById('emailForm');
  const validation = checkRequiredInputs(form);
  
  if (!validation.isValid) {
    // Handle invalid form
    console.log('Please fill out all required fields');
    
    // Optional: highlight empty fields
    validation.emptyFields.forEach(field => {
        console.log(field);

      field.classList.add('error-highlight');
    });
    
    return false;
    event.preventDefault(); 

  }
  const recipientEmail = document.getElementById('recipientEmailaccepter').value;
  const subject = document.getElementById('emailSubject').value;
  var message = document.getElementById('emailMessage').value;
  const dynamicFields = document.querySelectorAll('#dynamicFieldsContainer input[required]');
  for (let i = 0; i < dynamicFields.length; i++) {
    if (dynamicFields[i].value.trim() === '') {
      alert('Veuillez remplir tous les champs obligatoires.');
      return false;
    }
  }

  if ( !recipientEmail || !subject || !message) {
    alert('Veuillez remplir tous les champs obligatoires.');
    return false;
  }

  
  function cleanupUnreplacedVariables(message) {
  const regex = /\[(\w+)\]/g;

  message=message.replace(regex, '');
  console.log(message);
  message=message.replace('<br> <br> <br>', '<br>');

  return message;
}


message = cleanupUnreplacedVariables(message);

  document.getElementById('preview-subject').textContent = subject;
  document.getElementById('preview-message').innerHTML = message.replace(/\n/g, '<br>');
  // ---- AJOUT ICI : Liste des pièces jointes
  const fileInput = document.getElementById('emailAttachment');
  const fileList = fileInput?.files || [];
  console.log('File input:', fileInput);
  console.log('Selected files:', fileList);
  const attachmentList = document.getElementById('attachment-names');
  attachmentList.innerHTML = '';

  if (fileList.length > 0) {
    for (let i = 0; i < fileList.length; i++) {
      const listItem = document.createElement('li');
      listItem.textContent = fileList[i].name;
      attachmentList.appendChild(listItem);
    }
  } else {
    const listItem = document.createElement('li');
    listItem.textContent = 'Aucune pièce jointe';
    attachmentList.appendChild(listItem);
  }
  const previewModal = new bootstrap.Modal(document.getElementById('emailPreviewModal'));
  previewModal.show();
  
  return false; 
}
document.addEventListener('DOMContentLoaded', function() {


const emailForm = document.getElementById('accepter')?.querySelector('#emailForm');

if (emailForm) { // Vérifie si le formulaire existe
    const submitButton = emailForm.querySelector('button[type="submit"]');

    // Vérifie aussi que le bouton submit existe avant d'insérer avant lui
    if (submitButton) {
        // Vérifie si le bouton existe déjà pour éviter les doublons
        let previewButton = emailForm.querySelector('button.preview-button-js');
        if (!previewButton) {
            previewButton = document.createElement('button');
            previewButton.type = 'button';
            previewButton.className = 'btn btn-secondary me-2 preview-button-js'; // Classe pour l'identifier
            previewButton.onclick = previewEmail;

            // *** AJOUT : Ajoute les attributs de traduction ***
            previewButton.setAttribute('data-translate-fr', 'Prévisualiser');
            previewButton.setAttribute('data-translate-en', 'Preview');

            // *** IMPORTANT : Ne pas définir le texte ici ***
            // Le texte sera défini par translateStaticElements() lors de l'ouverture du modal ou du changement de langue.

            submitButton.parentNode.insertBefore(previewButton, submitButton);
        }
    } else {
        console.error("Le bouton 'Envoyer' (submit) n'a pas été trouvé dans le formulaire.");
    }

} else {
    // C'est normal si le modal n'est pas toujours dans le DOM initial
    // console.log("Le formulaire #emailForm dans #accepter n'est pas présent au chargement initial.");
}


// --- Reste du code DOMContentLoaded ---

// Initialise l'écouteur pour le bouton 'Envoyer' de la prévisualisation (si le modal existe)
const sendPreviewButton = document.getElementById('sendEmailFromPreview');
if(sendPreviewButton) {
    sendPreviewButton.addEventListener('click', function() {
        const previewModalEl = document.getElementById('emailPreviewModal');
        if (previewModalEl) {
            const previewModal = bootstrap.Modal.getInstance(previewModalEl);
             if (previewModal) {
                previewModal.hide();
             }
        }

        const mainEmailForm = document.getElementById('emailForm');
        if (mainEmailForm) {
            mainEmailForm.submit();
        } else {
             console.error("Impossible de trouver le formulaire principal #emailForm pour soumettre.")
        }
    });
}


// Les écouteurs pour les champs dynamiques (à déplacer ici si ce n'est pas déjà fait)
// NOTE : Ces champs sont ajoutés DYNAMIQUEMENT, donc il faut peut-être utiliser
// la délégation d'événements ou les ajouter dans `displayDynamicFields`
// MAIS `addDynamicFieldListeners` semble déjà gérer ça. Assure-toi qu'elle est appelée
// après l'ajout des champs (ce qui est le cas dans `displayDynamicFields`).


// Initialisation des écouteurs pour les éléments déjà présents au chargement (s'il y en a hors modal)

});

function checkRequiredInputs(formElement) {
  // Get all required inputs, selects, and textareas
  const requiredInputs = formElement.querySelectorAll('input[required], select[required], textarea[required]');
  
  // Check if all required elements have values
  let allFilled = true;
  let emptyInputs = [];
  
  requiredInputs.forEach(input => {
    input.classList.remove('error-highlight');
    if ((input.type === 'checkbox' || input.type === 'radio') && !input.checked) {
      allFilled = false;
      emptyInputs.push(input);
    } 
    // For other input types, check if they have a value
    else if (input.value.trim() === '') {
      allFilled = false;
      emptyInputs.push(input);
    }
  });
  
  return {
    isValid: allFilled,
    emptyFields: emptyInputs
  };
}


document.addEventListener('DOMContentLoaded', function() {
    // Get the select element
    const siteSelect = document.querySelector('select[name="source"]');
    
    // Add change event listener
    siteSelect.addEventListener('change', function() {
        // Get current URL
        let url = new URL(window.location.href);
        
        // Update or add the source parameter
        url.searchParams.set('source', this.value);
        
        // Preserve other existing parameters including filter/status
        const existingParams = new URLSearchParams(window.location.search);
        existingParams.forEach((value, key) => {
            if (key !== 'source') {
                url.searchParams.set(key, value);
            }
        });
        
        // Redirect to new URL
        window.location.href = url.toString();
    });

    // Add click event listeners to status filter links
    document.querySelectorAll('.list-actions').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            let url = new URL(window.location.href);
            const filter = this.getAttribute('data-filter');
            
            // Set the filter parameter
            if (filter) {
                url.searchParams.set('filter', filter);
            }
            
            // Preserve the current source parameter
            const currentSource = siteSelect.value;
            if (currentSource) {
                url.searchParams.set('source', currentSource);
            }
            
            // Preserve other relevant parameters
            const existingParams = new URLSearchParams(window.location.search);
            existingParams.forEach((value, key) => {
                if (key !== 'filter' && key !== 'source') {
                    url.searchParams.set(key, value);
                }
            });
            
            window.location.href = url.toString();
        });
    });
});

  </script>