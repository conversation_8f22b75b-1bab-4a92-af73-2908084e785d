@extends('admin.layout')
@section('content')

      <!--  BEGIN MAIN CONTAINER  -->

      <div class="main-container admin p-0" id="container">



            <div class="overlay"></div>
            <div class="cs-overlay"></div>
            <div class="search-overlay"></div>
        
            <!--  BEGIN CONTENT AREA  -->
            <div id="content" class="main-content">
                  <div class="layout-px-spacing">

                      <div class="middle-content w-100 p-0">               
          
                          <div class="row layout-top-spacing">
                              <div class="col-xl-12 col-lg-12 col-md-12">
          
                                        @if(session('success'))
                                        <div id="flash-message" class="alert alert-success">
                                            {{ session('success') }}
                                        </div>
                                      @endif
                                    <div class="mail-box-container">
                                        <div class="mail-overlay"></div>
            
                                        <div class="tab-title">
                                            <div class="row">
                                                <div class="col-md-12 col-sm-12 col-12 text-center">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-clipboard"><path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path><rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect></svg>
                                                    <h5 class="app-title">Candidats</h5>
                                                </div> 
                                                <div class="col-md-12 col-sm-12 col-12 ps-0 pe-0">
                                                  <div class="todoList-sidebar-scroll mt-4">
                                                    <!-- Main collapsible menu -->
                                                    <div class="accordion" id="sidebarAccordion">

                                                      @php
                                                      $currentRoute = Route::currentRouteName();
                                                      $currentSource = request()->get('source');
                                                      $currentFilter = request()->get('filter');
                                                    @endphp
                                                    
                                                      {{-- <li class="nav-item">
                                                          <a class="nav-link {{ $currentRoute === 'admin.dashboard.demandes'  ? 'active' : '' }}" 
                                                             href="{{ route('admin.dashboard.demandes', ['source' => 'africatalents']) }}">Missions AT</a>
                                                      </li> --}}
                                   
                                                        @if(Auth::user()->type === 'admin')
                                                        <div class="accordion-item">
                                                          <h2 class="accordion-header" id="headingMissions">
                                                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseMissions" aria-expanded="true" aria-controls="collapseMissions">
                                                              Candidats AW 
                                                            </button>
                                                          </h2>
                                                          <div id="collapseMissions" class="accordion-collapse collapse {{$currentSource === 'africawork' ? 'show' : ''}}" aria-labelledby="headingMissions" data-bs-parent="#sidebarAccordion">
                                                            <div class="accordion-body p-0">
                                                              <ul class="nav nav-pills d-block" id="pills-tab" role="tablist">
                                                                <li class="nav-item">
                                                                  <a class="nav-link {{ $currentRoute === 'admin.dashboard.candidats' && $currentSource === 'africawork' && $currentFilter==='en-attente' ? 'active' : '' }}" id="todo-task-done"  href="{{ route('admin.dashboard.candidats', ['source' => 'africawork','filter'=>'en-attente']) }}" role="tab" aria-selected="false">
                                                                    En attente {{$missions_count['africawork']['en_attente']}}
                                                                  </a>
                                                                </li>
                                                                <li class="nav-item">
                                                                  <a class="nav-link {{ $currentRoute === 'admin.dashboard.candidats' && $currentSource === 'africawork' && $currentFilter==='assignee' ? 'active' : '' }}" id="todo-task-important"  href="{{ route('admin.dashboard.candidats', ['source' => 'africawork','filter'=>'assignee']) }}" role="tab" aria-selected="false">
                                                                    Assigné {{$missions_count['africawork']['assignee']}}
                                                                  </a>
                                                                </li>
                                                                <li class="nav-item">
                                                                  <a class="nav-link {{ $currentRoute === 'admin.dashboard.candidats' && $currentSource === 'africawork' && $currentFilter==='traitee' ? 'active' : '' }}" id="todo-task-trash"  href="{{ route('admin.dashboard.candidats', ['source' => 'africawork','filter'=>'traitee']) }}" role="tab" aria-selected="false">
                                                                    Traité {{$missions_count['africawork']['traitee']}}
                                                                  </a>
                                                                </li>
                                                                <li class="nav-item">
                                                                  <a class="nav-link {{ $currentRoute === 'admin.dashboard.candidats' && $currentSource === 'africawork' && $currentFilter==='refusee' ? 'active' : '' }}" id="todo-task-trash"  href="{{ route('admin.dashboard.candidats', ['source' => 'africawork','filter'=>'refusee']) }}" role="tab" aria-selected="false">
                                                                    Refusé {{$missions_count['africawork']['refuser']}}
                                                                  </a>
                                                                </li>
                                                              </ul>
                                                            </div>
                                                          </div>
                                                        </div>
                                                        <div class="accordion-item">


                                                          <h2 class="accordion-header" id="headingMissions">
                                                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseMissions" aria-expanded="true" aria-controls="collapseMissions">
                                                              Candidats AS
                                                            </button>
                                                          </h2>
                                                          <div id="collapseMissions" class="accordion-collapse collapse {{$currentSource === 'africashore' ? 'show' : ''}}" aria-labelledby="headingMissions" data-bs-parent="#sidebarAccordion">
                                                            <div class="accordion-body p-0">
                                                              <ul class="nav nav-pills d-block" id="pills-tab" role="tablist">
                                                                <li class="nav-item">
                                                                  <a class="nav-link {{ $currentRoute === 'admin.dashboard.candidats' && $currentSource === 'africashore' && $currentFilter==='en-attente' ? 'active' : '' }}" id="todo-task-done"  href="{{ route('admin.dashboard.candidats', ['source' => 'africashore','filter'=>'en-attente']) }}" role="tab" aria-selected="false">
                                                                    En attente {{$missions_count['africashore']['en_attente']}}
                                                                  </a>
                                                                </li>
                                                                <li class="nav-item">
                                                                  <a class="nav-link {{ $currentRoute === 'admin.dashboard.candidats' && $currentSource === 'africashore' && $currentFilter==='assignee' ? 'active' : '' }}" id="todo-task-important"  href="{{ route('admin.dashboard.candidats', ['source' => 'africashore','filter'=>'assignee']) }}" role="tab" aria-selected="false">
                                                                    Assigné {{$missions_count['africashore']['assignee']}}
                                                                  </a>
                                                                </li>
                                                                <li class="nav-item">
                                                                  <a class="nav-link {{ $currentRoute === 'admin.dashboard.candidats' && $currentSource === 'africashore' && $currentFilter==='traitee' ? 'active' : '' }}" id="todo-task-trash"  href="{{ route('admin.dashboard.candidats', ['source' => 'africashore','filter'=>'traitee']) }}" role="tab" aria-selected="false">
                                                                    Traité {{$missions_count['africashore']['traitee']}}
                                                                  </a>
                                                                </li>
                                                                <li class="nav-item">
                                                                  <a class="nav-link {{ $currentRoute === 'admin.dashboard.candidats' && $currentSource === 'africashore' && $currentFilter==='refusee' ? 'active' : '' }}" id="todo-task-trash"  href="{{ route('admin.dashboard.candidats', ['source' => 'africashore','filter'=>'refusee']) }}" role="tab" aria-selected="false">
                                                                    Refusé {{$missions_count['africashore']['refuser']}}
                                                                  </a>
                                                                </li>
                                                              </ul>
                                                            </div>
                                                          </div>
                                                        </div>


                                                      {{-- <li class="nav-item">
                                                          <a class="nav-link {{ $currentRoute === 'admin.dashboard.candidats' && $currentSource === 'africawork' ? 'active' : '' }}" 
                                                             href="{{ route('admin.dashboard.candidats', ['source' => 'africawork']) }}">Missions AW</a>
                                                      </li>
                                                      <li class="nav-item">
                                                          <a class="nav-link {{ $currentRoute === 'admin.dashboard.candidats' && $currentSource === 'africashore' ? 'active' : '' }}" 
                                                             href="{{ route('admin.dashboard.candidats', ['source' => 'africashore']) }}">Missions AS</a>
                                                      </li> --}}
                                                      @endif 
                                                      @if(Auth::user()->type === 'responsable' && Auth::user()->hasPermission('africawork') )
                                                      <div class="accordion-item">
                                                        <h2 class="accordion-header" id="headingMissions">
                                                          <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseMissions" aria-expanded="true" aria-controls="collapseMissions">
                                                            Candidats AW
                                                          </button>
                                                        </h2>
                                                        <div id="collapseMissions" class="accordion-collapse collapse {{$currentSource === 'africawork' ? 'show' : ''}}" aria-labelledby="headingMissions" data-bs-parent="#sidebarAccordion">
                                                          <div class="accordion-body p-0">
                                                            <ul class="nav nav-pills d-block" id="pills-tab" role="tablist">
                                                              <li class="nav-item">
                                                                <a class="nav-link {{ $currentRoute === 'admin.dashboard.candidats' && $currentSource === 'africawork' && $currentFilter==='en-attente' ? 'active' : '' }}" id="todo-task-done"  href="{{ route('admin.dashboard.candidats', ['source' => 'africawork','filter'=>'en-attente']) }}" role="tab" aria-selected="false">
                                                                  En attente {{$missions_count['africawork']['en_attente']}}
                                                                </a>
                                                              </li>
                                                              <li class="nav-item">
                                                                <a class="nav-link {{ $currentRoute === 'admin.dashboard.candidats' && $currentSource === 'africawork' && $currentFilter==='assignee' ? 'active' : '' }}" id="todo-task-important"  href="{{ route('admin.dashboard.candidats', ['source' => 'africawork','filter'=>'assignee']) }}" role="tab" aria-selected="false">
                                                                  Assigné {{$missions_count['africawork']['assignee']}}
                                                                </a>
                                                              </li>
                                                              <li class="nav-item">
                                                                <a class="nav-link {{ $currentRoute === 'admin.dashboard.candidats' && $currentSource === 'africawork' && $currentFilter==='traitee' ? 'active' : '' }}" id="todo-task-trash"  href="{{ route('admin.dashboard.candidats', ['source' => 'africawork','filter'=>'traitee']) }}" role="tab" aria-selected="false">
                                                                  Traité {{$missions_count['africawork']['traitee']}}
                                                                </a>
                                                              </li>
                                                              <li class="nav-item">
                                                                <a class="nav-link {{ $currentRoute === 'admin.dashboard.candidats' && $currentSource === 'africawork' && $currentFilter==='refusee' ? 'active' : '' }}" id="todo-task-trash"  href="{{ route('admin.dashboard.candidats', ['source' => 'africawork','filter'=>'refusee']) }}" role="tab" aria-selected="false">
                                                                  Refusé {{$missions_count['africawork']['refuser']}}
                                                                </a>
                                                              </li>
                                                            </ul>
                                                          </div>
                                                        </div>
                                                      </div>
                                                      @endif 
                                                       @if(Auth::user()->type === 'responsable' && Auth::user()->hasPermission('africashore') )
                                                       <div class="accordion-item">


                                                        <h2 class="accordion-header" id="headingMissions">
                                                          <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseMissions" aria-expanded="true" aria-controls="collapseMissions">
                                                            Candidats AS
                                                          </button>
                                                        </h2>
                                                        <div id="collapseMissions" class="accordion-collapse collapse {{$currentSource === 'africashore' ? 'show' : ''}}" aria-labelledby="headingMissions" data-bs-parent="#sidebarAccordion">
                                                          <div class="accordion-body p-0">
                                                            <ul class="nav nav-pills d-block" id="pills-tab" role="tablist">
                                                              <li class="nav-item">
                                                                <a class="nav-link {{ $currentRoute === 'admin.dashboard.candidats' && $currentSource === 'africashore' && $currentFilter==='en-attente' ? 'active' : '' }}" id="todo-task-done"  href="{{ route('admin.dashboard.candidats', ['source' => 'africashore','filter'=>'en-attente']) }}" role="tab" aria-selected="false">
                                                                  En attente {{$missions_count['africashore']['en_attente']}}
                                                                </a>
                                                              </li>
                                                              <li class="nav-item">
                                                                <a class="nav-link {{ $currentRoute === 'admin.dashboard.candidats' && $currentSource === 'africashore' && $currentFilter==='assignee' ? 'active' : '' }}" id="todo-task-important"  href="{{ route('admin.dashboard.candidats', ['source' => 'africashore','filter'=>'assignee']) }}" role="tab" aria-selected="false">
                                                                  Assigné {{$missions_count['africashore']['assignee']}}
                                                                </a>
                                                              </li>
                                                              <li class="nav-item">
                                                                <a class="nav-link {{ $currentRoute === 'admin.dashboard.candidats' && $currentSource === 'africashore' && $currentFilter==='traitee' ? 'active' : '' }}" id="todo-task-trash"  href="{{ route('admin.dashboard.candidats', ['source' => 'africashore','filter'=>'traitee']) }}" role="tab" aria-selected="false">
                                                                  Traité {{$missions_count['africashore']['traitee']}}
                                                                </a>
                                                              </li>
                                                              <li class="nav-item">
                                                                <a class="nav-link {{ $currentRoute === 'admin.dashboard.candidats' && $currentSource === 'africashore' && $currentFilter==='refusee' ? 'active' : '' }}" id="todo-task-trash"  href="{{ route('admin.dashboard.candidats', ['source' => 'africashore','filter'=>'refusee']) }}" role="tab" aria-selected="false">
                                                                  Refusé {{$missions_count['africashore']['refuser']}}
                                                                </a>
                                                              </li>
                                                            </ul>
                                                          </div>
                                                        </div>
                                                      </div>
                                                       @endif     
                                                
                                                      





                                                      <!-- Mission Status Section -->
                                                    
                                                      
                                                      <!-- Admin Section -->
                                                      {{-- <div class="accordion-item">
                                                        <h2 class="accordion-header" id="headingAdmin">
                                                          <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseAdmin" aria-expanded="false" aria-controls="collapseAdmin">
                                                            Ajouter un modérateur
                                                          </button>
                                                        </h2>
                                                        <div id="collapseAdmin" class="accordion-collapse collapse" aria-labelledby="headingAdmin" data-bs-parent="#sidebarAccordion">
                                                          <div class="accordion-body">
                                                            <button type="button" class="btn btn-primary w-100" data-bs-toggle="modal" data-bs-target="#registerUserModal">
                                                              Ajouter un modérateur
                                                            </button>
                                                          </div>
                                                        </div>
                                                      </div> --}}
                                                    </div>
                                                  </div>
                                                </div>
                                                <div class="col-md-12 col-sm-12 col-12">
                                                
                                                </div>
                                            </div>
                                        </div>
                                        <div id="todo-inbox" class="accordion todo-inbox">
                                        
                                          <div class="container mt-4 ">
                                          
                                            <div class="row mt-4">
                                              <div class="col-12">
                                                <div class="tab-content" id="pills-tabContent">
                                                  
                                                  <!-- Toutes les missions -->
                                                  <div class="tab-pane fade show active" id="pills-all" role="tabpanel">
                                                  <div class="todo-box">
                                                    <div id="ct" class="todo-box-scroll">
                                                      <div class="d-flex mb-2 admin-filter">
                                                        <form method="GET" action="{{ url()->current() }}" class="d-flex align-items-center w-100">
                                                          <?php $filter = isset($_GET["filter"]) ? $_GET["filter"] : null;  ?>
                                                            <input type="hidden" name="filter" value="{{$filter}}">
                                                      
                                                            <div class="d-flex ms-4 me-2 w-25">
                                                            <select class="form-select" name="moderateur_id" aria-label="Default select example">
                                                                <option  value="">Modérateur</option>
                                                                @foreach ($moderateurs as $moderateur)
                                                                <option value="{{ $moderateur->id }}" {{ request('moderateur_id') == $moderateur->id ? 'selected' : '' }}>{{ $moderateur->name }}</option>
                                                              @endforeach
                                                            </select>
                                                        </div>
                                                        <div>
                                                          <input type="datetime-local" class="form-control me-2 w-auto" name="debut_mission" aria-label="Start DateTime"  value="{{ request('date') }}">
                  
                                                        </div>
                                                        <div>
                                                          <button type="submit" class="btn btn-primary">Filtrer</button>
                                                        </div>
                                                    </form>
                  
                                                    </div>
                                                        <div class="accordion accordion-flush pe-3" id="accordionFlushExample">
                                                            @foreach ($missions as $index =>$mission)
                                                                <div class="accordion-item">
                                                                    <h2 class="accordion-header d-flex align-items-center" id="heading{{$mission->id}}">
                                                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{$mission->id}}" aria-expanded="false" aria-controls="collapse{{$mission->id}}">
                                                                            <div class="row  w-100 aprc-wrapper">
                                                                              
                                                                                <div class="col-md-2 aprc-prestation">
                                                                                  <p>Site : {{ ucfirst($mission->source) }}</p>
                                                                                  <p>Nr : {{ $mission->formatted_number }}</p>
                                                                                  
                                                                                    
                                                                                </div>
                                                                                <div class="col-md-2 aprc-prestation">
                                                                                  <p>Date : {{ \Carbon\Carbon::parse($mission->create)->format('Y-m-d') }}</p>
                                                                                  <p>Heure : {{ \Carbon\Carbon::parse($mission->create)->format('H:i') }}</p>
                                                                                    
                                                                                </div>
                                                                                <div class="col-md-3 aprc-company aprc-tous">
                                                                                    <p>Entreprise</p>
                                                                                    <h5>{{$mission->nom_entreperise}}</h5> 
                                                                                </div>
                                                                                <div class="col-md-3 aprc-description">
                                                                                    <p>Description</p>
                                                                                    <h5>{{$mission->description}}</h5> 
                                                                                </div>
                                                                                @if($mission->source !='africawork')
                                                                                <div class=" col-md-1  aprc-duree">
                                                                                    <p>Durée : {{$mission->duree_mission}}</p>
                                                                                    {{-- <h5>{{$mission->duree_mission}}</h5>  --}}
                                                                                </div>
                                                                                <div class=" col-lg-2 aprc-debut">
                                                                                    <p>Début : {{$mission->debut_mission}}</p>
                                                                                    {{-- <h5>{{$mission->debut_mission}}</h5>  --}}
                                                                                </div>
                                                                                <div class="col-lg-2 aprc-lieu">
                                                                                    <p>Lieu : {{$mission->lieu_mission}}</p>
                                                                                    {{-- <h5>{{$mission->lieu_mission}}</h5>  --}}
                                                                                </div>
                                                                            @endif
                                                                            </div>
                                                                        </button>
                                                                        <div class="assignee2">
                                                                        


                                                                          @if ($mission->status ==='traitée' && $mission->etat==='accepter')
                                                                          <div class="traitee">
                                                                            <p>Traité par</p>
                                                                            <h5>{{$mission->moderateur->name}}</h5> 
            
                                                                        </div>

                                                                        @elseif($mission->status ==='traitée' && $mission->etat==='refuser')
                                                                        <div id="mission_refuser" class="traitee">
                                                                          <p>Refusé par</p>
                                                                          <h5>{{$mission->moderateur->name}}</h5> 
          
                                                                      </div>
                                                                      <div class="">
                                                                        <select class="form-select select_moderateurs" aria-label="Assigner un modérateur" onchange="assignMissionrefuser({{ $mission->id }}, this)">
                                                                            <option selected>Choisir </option>
                                                                            @foreach ($moderateurs as $moderateur)
                                                                                <option value="{{ $moderateur->id }}">{{ $moderateur->name }}</option>
                                                                            @endforeach
                                                                        </select>
                                                                        <div id="loading-{{$mission->id}}" class="spinner-border text-primary mt-2 d-none" role="status">
                                                                            <span class="visually-hidden">Chargement...</span>
                                                                        </div>
                                                                    </div>
                                                                    
                                                                          @elseif($mission->status ==='assignée' ) 
                                                                        
                                                                          <div class="traitee">
                                                                            <p>Assigné à </p>
                                                                          </div>
                                                                          <div class="">
                                                                            <select class="form-select select_moderateurs" aria-label="Assigner un modérateur" onchange="assignMissionAutre({{ $mission->id }}, this)">
                                                                                @foreach ($moderateurs as $moderateur)
                                                                                    <option value="{{ $moderateur->id }}"   {{ $mission->user_id == $moderateur->id ? 'selected' : '' }} >{{ $moderateur->name }}</option>
                                                                                @endforeach
                                                                            </select>
                                                                            <div id="loading-{{$mission->id}}" class="spinner-border text-primary mt-2 d-none" role="status">
                                                                                <span class="visually-hidden">Chargement...</span>
                                                                            </div>
                                                                        </div>

                                                                          @else



                                                                          <div class="">
                                                                            <select class="form-select select_moderateurs" aria-label="Assigner un modérateur" onchange="assignMission({{ $mission->id }}, this)">
                                                                                <option selected>Choisir </option>
                                                                                @foreach ($moderateurs as $moderateur)
                                                                                    <option value="{{ $moderateur->id }}"   {{ $mission->user_id == $moderateur->id ? 'selected' : '' }} >{{ $moderateur->name }}</option>
                                                                                @endforeach
                                                                            </select>
                                                                            <div id="loading-{{$mission->id}}" class="spinner-border text-primary mt-2 d-none" role="status">
                                                                                <span class="visually-hidden">Chargement...</span>
                                                                            </div>
                                                                        </div>

                                                                          @endif







                                                                        </div>
                                                                    </h2>
                                                                    <div id="collapse{{$mission->id}}" class="accordion-collapse collapse" aria-labelledby="heading{{$mission->id}}" data-bs-parent="#accordionFlushExample">
                                                                        <div class="accordion-body">
                                                                            <div class="d-flex flex-column">
                                                                                <!-- Détails de la mission -->
                                                                                <div class="d-flex flex-column mt-3">
                                                                                    <div>
                                                                                      <p>Prestation</p>
                                                                                      <h5>{{$mission->prestation ?$mission->prestation:'-'}}</h5> 
                                                                                    </div>
                                                                                </div>
                                                                                <!-- Si la mission est déjà assignée -->
                                                                            
                                                                                <!-- Description -->
                                                                                <div class="d-flex flex-column ">
                                                                                    <div>
                                                                                      <p>Description</p>
                                                                                      <h5>{{$mission->description}}</h5> 
                                                                                    </div>
                                                                                </div>
                                                                                
                                                                                <div class="d-flex flex-row justify-content-between w-100">
                                                                                    {{-- <div>
                                                                                        <h5>{{$mission->debut_mission}}</h5> 
                                                                                        <p>Début mission</p>
                                                                                    </div>
                                                                                    <div>
                                                                                        <h5>{{$mission->duree_mission}}</h5> 
                                                                                        <p>Durée mission</p>
                                                                                    </div>
                                                                                    <div>
                                                                                        <h5>{{$mission->lieu_mission}}</h5> 
                                                                                        <p>Lieu mission</p>
                                                                                    </div> --}}
                                                                                    <div>
                                                                                      <p>Ville</p>
                                                                                      <h5>{{$mission->ville ? $mission->ville:'-'}}</h5> 
                                                                                    </div>
                                                                                </div>

                                                                                @if ($mission->status ==='traitée' && $mission->etat === 'refuser')
                                                                                <div class="d-flex flex-column mt-3 alert alert-danger p-2">
                                                                                    <p class="mb-1"><strong>Cette mission a été refusée.</strong></p>
                                                                                    @if($mission->moderateur)
                                                                                    <p class="mb-1">Refusée par : <strong>{{ $mission->moderateur->name }}</strong></p>
                                                                                    @endif
                                                                                    @if($mission->motif_refus)
                                                                                    <p class="mb-0">Motif : {{ $mission->motif_refus }}</p>
                                                                                    @endif
                                                                                </div>
                                                                            @endif
                                                                              

                                                                            @if ($mission->status ==='traitée')
                                                                            {{-- <div class="d-flex flex-column mt-3">
                                                                            <h5>{{$mission->moderateur->name}}</h5> 
                                                                            <p>traité par</p>
              
                                                                          </div> --}}



                                                                          
                                                                            @elseif($mission->status ==='assignée' && (Auth::user()->type ==='admin' || Auth::user()->id ==$mission->user_id) ) 
                                                                            {{-- <div class="d-flex flex-column mt-3">
                                                                            <div>
                                                                              <h5>{{$mission->moderateur->name}}</h5> 
                                                                              <p>assigné à </p>
                                                                            </div>
                                                                          </div>  --}}
                                                                          <div class="d-flex flex-column mt-3">
                                                                            <div class="d-flex">
                                                                              <button type="button" class="btn btn-success me-3" data-bs-toggle="modal" data-bs-target="#accepter" onclick='handleMissionAction("accepter",@json($mission))'>Rédiger le mail</button>
                                                                              {{-- <button type="button" class="btn btn-danger me-3" data-bs-toggle="modal" data-bs-target="#refuser" onclick='handleMissionEtat("refuser",@json($mission))'>Refuser</button> --}}
                                                                              <form action="{{ route('change.etat.mission') }}" method="POST" id="changeEtatMissionForm">
                                                                                @csrf
                                                                                <input type="hidden" name="etat" value="refuser" id="etatMission">
                                                                                <input type="hidden" name="id_mission" id="idMission" value="{{ $mission->id }}">
                                                                                {{-- <button type="submit" class="btn btn-danger me-3" >Refuser</button> --}}
                                                                                <button type="button" class="btn btn-danger me-3" data-bs-toggle="modal" data-bs-target="#refusMissionModal" onclick="prepareRefusModal({{ $mission->id }})">Refuser</button>
                                                                            </form>
                                                                              <button type="button" class="btn btn-btn-info" data-bs-toggle="modal" data-bs-target="#refuser" onclick='handleMissionEtat("ajouter_crm",@json($mission))'>Compte CRM</button>
                                                                            </div>
                                                                          </div>




                                                                          

                                                                            @endif




                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            @endforeach
                                                        </div>
                                                    </div>
                                                </div>
                                              </div>
                                          
                                              
                                          
                                            
                                          
                                                
                                                </div>
                                              </div>
                                            </div>
                                          </div>

                                        </div>    
                                                                        
                                    </div>
                                    @if ($missions->hasPages())
                                    {{ $missions->links('pagination::bootstrap-5') }}
                                    @else
                                    <div class="d-flex flex-start">
                                        <div >
                                          {{-- <p class="small text-muted">{{ $totalCount }} emails envoyés</p> --}}
                                          {{-- <p class="small text-muted">Showing {{ $totalCount }} of {{ $totalCount }} results</p> --}}
                                        </div>
                                    </div>
                                    @endif
                        
                                         <!-- The Modal -->
                                         <div class="modal fade modal-mail" id="accepter" tabindex="-1" aria-labelledby="emailModalLabel" aria-hidden="true">
                                          <div class="modal-dialog modal-xl">
                                            <div class="modal-content">
                                              
                                              <!-- Modal Header -->
                                              <div class="modal-header">
    
                                                <div class="sous-header-modal">
                                                  {{-- <h5 class="modal-title" id="emailModalLabel">Envoyer un email</h5> --}}
                                                    <div class="">
    
                                                      <span>Prestation</span><p id="modal_prestation" name="prestation" ></p>
                                                    </div>
                                                    <div class="">
                                                    <span>Description</span><p type="text" id="modal_decription" name="decription" ></p>
    
                                                    </div>
    
    
                                                </div>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                              </div>
                                              
                                              <!-- Modal Body -->
                                              {{-- <div class="modal-body">
                                                    <form class="row"    id="emailForm" action="/send-mission-email-admin" method="POST" enctype="multipart/form-data" onsubmit="return confirmSubmission()">
                                                      @csrf
                                                          <div class="col-md-5">
                                                              <input type="hidden" class="form-control" id="id_mission" name="id_mission"  required>
    
                                                              <select class="form-select mb-3" name="mail_id" id="mailTemplateSelect" aria-label="Small select example" onchange="updateEmailContent()" required>
                                                                <option selected disabled>Mail template</option>
                                                                @foreach ($mail_template as $item)
                                                                  <option value="{{$item->id}}" 
                                                                    
                                                                    data-subject-fr="{{$item->subject_fr}}" 
                                                                    data-message-fr="{{$item->template_fr}}"
                                                                    data-subject-en="{{$item->subject_en}}" 
                                                                    data-message-en="{{$item->template_en}}"
                                                                        data-variables_fr="{{$item->variables_fr}}"
                                                                        data-variables_en="{{$item->variables_en}}"
                                                                    >
                                                                    {{$item->type}}
                                                                  </option>
                                                                @endforeach
                                                              </select>
    
    
                                                              <div class="mb-3  d-flex justify-content-between  align-items-center">
                                                                <label for="languageSelect" class="form-label">Langue</label>
                                                                <select class="form-select input " id="languageSelect" name="langue" aria-label="Language select example" onchange="updateEmailContent()">
                                                                    <option value="fr" selected>Français</option>
                                                                    <option value="en">Anglais</option>
                                                                </select>
                                                            </div>
    
    
    
    
                                                              <!-- Recipient Email -->
                                                              <div class="mb-3 d-flex justify-content-between  align-items-center">
                                                                <label for="recipientEmailaccepter" class="form-label">Adresse email</label>
                                                                <input type="email"  name="to_email" class="form-control input" id="recipientEmailaccepter" placeholder="<EMAIL>" required>
                                                              </div>
                                                    
                                                              <!-- CC -->
                                                              <div class="mb-3  d-flex justify-content-between  align-items-center">
                                                                <label for="ccEmail" class="form-label">CC</label>
                                                                <input type="email" class="form-control input"  name="cc_email" id="ccEmail" placeholder="<EMAIL>">
                                                              </div>
                                                    
    
    
    
                                                              <div id="dynamicFieldsContainer" class="mb-3"></div>
    
    
                                                                      <hr>
    
                                                              <div class="mb-3  d-flex justify-content-between  align-items-center">
                                                                <label for="nom" class="form-label">Nom</label>
                                                                <input type="text" class="form-control input" id="nom" data-variable="nom" name="nom" onchange="replaceVariablesOnUserInput()"  placeholder="[nom]">
                                                            </div>
                                                            <div class="mb-3  d-flex justify-content-between  align-items-center">
                                                              <label for="prenom" class="form-label">Prénom</label>
                                                              <input type="text" class="form-control input" id="prenom" name="prenom" data-variable="prenom" placeholder="[prenom]">
                                                          </div>
                                                          <div class="mb-3  d-flex justify-content-between  align-items-center">
                                                            <label for="nom" class="form-label">Civilité</label>
                                                            <input type="text" class="form-control input" id="civilitee" name="civilitee" data-variable="civilitee" placeholder="[nom]">
                                                        </div>
                                                            <div class="mb-3  d-flex justify-content-between  align-items-center">
                                                                <label for="entreprise" class="form-label">Entreprise</label>
                                                                <input type="text" class="form-control input" id="entreprise" name="nom_entreperise" data-variable="nom_entreperise" placeholder="[entreprise]">
                                                            </div>
                                                            <div class="mb-3  d-flex justify-content-between  align-items-center">
                                                                <label for="telephone" class="form-label">Téléphone</label>
                                                                <input type="text" class="form-control input" id="telephone" name="telephone" placeholder="[telephone]">
                                                            </div>
    
                                                         
                                                          </div>
    
    
                                                          <div class="col-md-7">
                                                                    <!-- Subject -->
                                                                    <div class="mb-3">
                                                                      <label for="emailSubject" class="form-label">Sujet</label>
                                                                      <input type="text" class="form-control" id="emailSubject" name="subject" placeholder="Sujet de l'email" required>
                                                                    </div>
                                                                    
    
                                                                    <!-- Message -->
                                                                    <div class="mb-3">
                                                                      <div class="d-flex  justify-content-between">
    
                                                                        <label for="emailMessage" class="form-label">Message</label>
                                                                        <button  type="button" id="toggleButton" class="btn btn-secondary m-2" onclick="updateFieldsState()">activer la modification manuelle</button>
                                                                      </div>
                                                                      <textarea id="emailMessage" name="emailMessage" name="message" style="height: 400px;" class="form-control" readonly></textarea>
                                                                    </div>
    
                                                                    <!-- File Attachment -->
                                                                    <div class="mb-3">
                                                                      <label for="emailAttachment" class="form-label">Pièce jointe</label>
                                                                      <input type="file" class="form-control" name="attachments[]" id="emailAttachment">
                                                                    </div>
                                                                    <div class="mb-3">
                                                                      <button type="submit" class="btn btn-primary" form="emailForm">Envoyer</button>
    
                                                                    </div>
                                                          </div>
    
    
    
    
                                                        
                                                    </form>
    
    
    
                                                    <div class="row">
                                                                   
                                                    </div>
    
                                              
                                              </div> --}}
    
                                              <!-- Modal Body -->
    <div class="modal-body">
      <form class="row" id="emailForm" action="/send-mission-email-admin-candidats" method="POST" enctype="multipart/form-data" onsubmit="return confirmSubmission()">
          @csrf
          <div class="col-md-5">
              <input type="hidden" class="form-control" id="id_mission" name="id_mission" required>
    
              <select class="form-select mb-3" name="mail_id" id="mailTemplateSelect" aria-label="Small select example" onchange="updateEmailContent()" required>
                  <option selected disabled data-translate-fr="Mail template" data-translate-en="Email template" value="">Mail template</option>
                  @foreach ($mail_template as $item)
                  <option value="{{$item->id}}" 
                    
                    data-subject-fr="{{$item->subject_fr}}" 
                    data-message-fr="{{$item->template_fr}}"
                    data-subject-en="{{$item->subject_en}}" 
                    data-message-en="{{$item->template_en}}"
                        data-variables_fr="{{$item->variables_fr}}"
                        data-variables_en="{{$item->variables_en}}"
                    >
                    {{$item->type}}
                  </option>
                @endforeach
              </select>
    
              <div class="mb-3 d-flex justify-content-between align-items-center">
                  <label for="languageSelect" class="form-label" data-translate-fr="Langue" data-translate-en="Language">Langue</label>
                  <select class="form-select input" id="languageSelect" name="langue" aria-label="Language select example">
                      {{-- NOTE: onchange est déjà géré globalement, pas besoin ici --}}
                      <option value="fr" selected>Français</option>
                      <option value="en">English</option>
                  </select>
              </div>
    
              <!-- Recipient Email -->
              <div class="mb-3 d-flex justify-content-between align-items-center">
                  <label for="recipientEmailaccepter" class="form-label" data-translate-fr="Adresse email" data-translate-en="Email Address">Adresse email</label>
                  <input type="email" name="to_email" class="form-control input" id="recipientEmailaccepter"
                         data-translate-fr-placeholder="<EMAIL>"
                         data-translate-en-placeholder="<EMAIL>"
                         placeholder="<EMAIL>" required>
              </div>
    
              <!-- CC -->
              <div class="mb-3 d-flex justify-content-between align-items-center">
                  <label for="ccEmail" class="form-label" data-translate-fr="CC" data-translate-en="CC">CC</label>
                  <input type="email" class="form-control input" name="cc_email" id="ccEmail"
                         data-translate-fr-placeholder="<EMAIL>"
                         data-translate-en-placeholder="<EMAIL>"
                         placeholder="<EMAIL>">
              </div>
    
              <div id="dynamicFieldsContainer" class="mb-3"></div>
              <hr>
    
              <div class="mb-3 d-flex justify-content-between align-items-center">
                  <label for="nom" class="form-label" data-translate-fr="Nom" data-translate-en="Last Name">Nom</label>
                  <input type="text" class="form-control input" id="nom" data-variable="nom" name="nom" onchange="replaceVariablesOnUserInput()" placeholder="[nom]">
              </div>
              <div class="mb-3 d-flex justify-content-between align-items-center">
                  <label for="prenom" class="form-label" data-translate-fr="Prénom" data-translate-en="First Name">Prénom</label>
                  <input type="text" class="form-control input" id="prenom" name="prenom" data-variable="prenom" placeholder="[prenom]">
              </div>
              <div class="mb-3 d-flex justify-content-between align-items-center">
                  <label for="civilitee" class="form-label" data-translate-fr="Civilité" data-translate-en="Title">Civilité</label>
                  <input type="text" class="form-control input" id="civilitee" name="civilitee" data-variable="civilitee" placeholder="[nom]"> {{-- Placeholder might need update based on context --}}
              </div>
              <div class="mb-3 d-flex justify-content-between align-items-center">
                  <label for="entreprise" class="form-label" data-translate-fr="Entreprise" data-translate-en="Company">Entreprise</label>
                  <input type="text" class="form-control input" id="entreprise" name="nom_entreperise" data-variable="nom_entreperise" placeholder="[entreprise]">
              </div>
              <div class="mb-3 d-flex justify-content-between align-items-center">
                  <label for="telephone" class="form-label" data-translate-fr="Téléphone" data-translate-en="Phone">Téléphone</label>
                  <input type="text" class="form-control input" id="telephone" name="telephone" placeholder="[telephone]">
              </div>
          </div>
    
          <div class="col-md-7">
              <!-- Subject -->
              <div class="mb-3">
                  <label for="emailSubject" class="form-label" data-translate-fr="Sujet" data-translate-en="Subject">Sujet</label>
                  <input type="text" class="form-control" id="emailSubject" name="subject"
                         data-translate-fr-placeholder="Sujet de l'email"
                         data-translate-en-placeholder="Email subject"
                         placeholder="Sujet de l'email" required>
              </div>
    
              <!-- Message -->
              <div class="mb-3">
                  <div class="d-flex justify-content-between">
                      <label for="emailMessage" class="form-label" data-translate-fr="Message" data-translate-en="Message">Message</label>

                      <button type="button" id="toggleButton" class="btn btn-secondary m-2"
                              onclick="updateFieldsState()"
                              data-translate-fr-manual="activer la modification manuelle"
                              data-translate-en-manual="Enable manual editing"
                              data-translate-fr-vars="activer les variables"
                              data-translate-en-vars="Enable variables">activer la modification manuelle</button>
                  </div>
                  <textarea id="emailMessage" name="emailMessage" style="height: 400px;" class="form-control" readonly></textarea>
              </div>
    
              <div class="mb-3">
                  <label for="emailAttachment" class="form-label" data-translate-fr="Pièce jointe" data-translate-en="Attachment">Pièce jointe</label>
                  <input type="file" class="form-control" name="attachments[]" id="emailAttachment"  multiple="multiple">
              </div>
              <div class="mb-3">
                  <button type="submit" class="btn btn-primary" form="emailForm" data-translate-fr="Envoyer" data-translate-en="Send">Envoyer</button>
              </div>
          </div>
      </form>
    </div>
    
    <!-- Modal Footer -->
    <div class="modal-footer">
      <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" data-translate-fr="Fermer" data-translate-en="Close">Fermer</button>
    </div>
                                              
                                              <!-- Modal Footer -->
                                              {{-- <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                                              </div> --}}
                                              
                                            </div>
                                          </div>
                                        </div>
          
                                  </div>
                              </div>
    
                          </div>
    
                          
                      </div>
                    
              
                </div>
            <!--  END CONTENT AREA  -->
          </div>
        <!-- END MAIN CONTAINER -->
    
    
    
        <div class="modal fade" id="registerUserModal" tabindex="-1" aria-labelledby="registerUserModalLabel" aria-hidden="true">
          <div class="modal-dialog">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title" id="registerUserModalLabel">Ajouter un utilisateur</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
              </div>
              <div class="modal-body">
                <form id="registerUserForm" action="{{ route('admin.register.user') }}" method="POST">
                  @csrf
                  <div class="mb-3">
                    <label for="name" class="form-label">Nom</label>
                    <input type="text" class="form-control" id="name" name="name" required>
                  </div>
                  <div class="mb-3">
                    <label for="fonction" class="form-label">Fonction</label>
                    <input type="text" class="form-control" id="fonction" name="fonction" >
                  </div>
                  <div class="mb-3">
                    <label for="telephone" class="form-label">Téléphone</label>
                    <input type="text" class="form-control" id="telephone" name="telephone" >
                  </div>
                  <div class="mb-3">
                    <label for="email" class="form-label">Email</label>
                    <input type="email" class="form-control" id="email" name="email" required>
                  </div>

                  {{-- <div class="mb-3">
                    <label for="site" class="form-label">Site</label>
                    <select class="form-select" id="site" name="site" required>
                      <option value="">Sélectionner un site</option>
                      <option value="africawork">Africawork</option>
                      <option value="africashore">africashore</option>
                      <option value="africatalents">Africatalents</option>
                    </select>
                  </div> --}}

                  {{-- <div class="row mb-3">
                    <label class="col-md-4 col-form-label text-md-end">{{ __('Permissions') }}</label> --}}

                    {{-- <div class="col-md-6">
                        @foreach($permissions as $permission)
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="permissions[]" value="{{ $permission->id }}" id="permission_{{ $permission->id }}">
                                <label class="form-check-label" for="permission_{{ $permission->id }}">
                                    {{ $permission->name }}
                                </label>
                            </div>
                        @endforeach
                    </div>
                </div> --}}

                  @if(Auth::user()->type === 'admin')
                  <div class="mb-3">
                    <label for="type" class="form-label">type</label>
                    <select class="form-select" id="type" name="type" required>
                      <option value="">Sélectionner un type</option>
                      <option value="moderateur" selected>Modérateur </option>
                      <option value="responsable">Responsable</option>
                    </select>
                  </div>
                  @endif
                  <div class="mb-3">
                    <label for="password" class="form-label">Mot de passe</label>
                    <input type="password" class="form-control" id="password" name="password" required>
                  </div>
                  <div class="mb-3">
                    <label for="password_confirmation" class="form-label">Confirmer le mot de passe</label>
                    <input type="password" class="form-control" id="password_confirmation" name="password_confirmation" required>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                    {{-- <button type="button" class="btn btn-secondary me-2" onclick="previewEmail()">Prévisualiser</button> --}}
    
                    <button type="submit" class="btn btn-primary">Enregistrer</button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    <div class="modal fade" id="emailPreviewModal" tabindex="-1" aria-labelledby="emailPreviewModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="emailPreviewModalLabel">Aperçu de l'email</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <div class="card">
              <div class="card-header">
                {{-- <div class="row">
                  <div class="col-md-6">
                    <strong>De:</strong> <span id="preview-from"><EMAIL></span>
                  </div>
                  <div class="col-md-6">
                    <strong>À:</strong> <span id="preview-to"></span>
                  </div>
                </div> --}}
                {{-- <div class="row mt-2">
                  <div class="col-md-6">
                    <strong>CC:</strong> <span id="preview-cc"></span>
                  </div>
                  <div class="col-md-6">
                    <strong>Date:</strong> <span id="preview-date"></span>
                  </div>
                </div> --}}
                <div class="row mt-2">
                  <div class="col-12">
                    <strong>Sujet:</strong> <span id="preview-subject"></span>
                  </div>
                </div>
              </div>
              <div class="card-body">
                <div id="preview-message" class="p-3" style="min-height: 300px; border: 1px solid #e0e0e0;"></div>
                {{-- <div class="mt-3" id="preview-attachments">
                  <strong>Pièces jointes:</strong>
                  <ul id="preview-attachment-list"></ul>
                </div> --}}
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
            <button type="button" class="btn btn-primary" id="sendEmailFromPreview">Envoyer</button>
          </div>
        </div>
      </div>
    </div>
    






    <!-- Modal Refus Mission -->
<div class="modal fade" id="refusMissionModal" tabindex="-1" aria-labelledby="refusMissionModalLabel" aria-hidden="true">
  <div class="modal-dialog">
      <div class="modal-content">
          <div class="modal-header">
              <h5 class="modal-title" id="refusMissionModalLabel">Motif de Refus de la Mission</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          {{-- Ensure this form posts to the correct route that handles mission state changes --}}
          <form id="refusMissionForm" method="POST" action="{{ route('change.etat.mission') }}">
              @csrf
              <div class="modal-body">
                  <input type="hidden" name="etat" value="refuser">
                  <input type="hidden" name="id_mission" id="refus_mission_id">
                  <div class="mb-3">
                      <label for="motif_refus_input" class="form-label">Veuillez indiquer le motif du refus (obligatoire) :</label>
                      <textarea class="form-control" id="motif_refus_input" name="motif_refus" rows="4" required></textarea>
                      <div class="invalid-feedback">
                          Le motif de refus est obligatoire.
                      </div>
                  </div>
              </div>
              <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                  <button type="submit" class="btn btn-danger">Confirmer le Refus</button>
              </div>
          </form>
      </div>
  </div>
</div>

    @endsection
    
    <script>
     function assignMission22(missionId, button) {
        var moderateurId = button.previousElementSibling.value;
        
        if (moderateurId) {
            $.ajax({
                url: '{{ route("assign.moderator") }}',
                type: 'POST',
                data: {
                    mission_id: missionId,
                    moderateur_id: moderateurId,
                    _token: '{{ csrf_token() }}' 
                },
                success: function(response) {
                    if (response.success) {
                        alert(response.message);
                        location.reload(); // 
                    }
                },
                error: function(xhr, status, error) {
                    alert('Une erreur est survenue. Veuillez réessayer.');
                    console.error(xhr.responseText);
                }
            });
        } else {
            alert('Veuillez choisir un modérateur');
        }
    }
    
    function assignMission(missionId, selectElement) {
        // Récupérer l'ID du modérateur sélectionné
        var moderateurId = selectElement.value;
        var moderateurName = selectElement.options[selectElement.selectedIndex].text;
        var loader = document.getElementById('loading-' + missionId);
        
        if (moderateurId) {
            loader.classList.remove('d-none');
            
            $.ajax({
                url: '{{ route("assign.moderator.candidats") }}',
                type: 'POST',
                data: {
                    mission_id: missionId,
                    moderateur_id: moderateurId,
                    _token: '{{ csrf_token() }}' // Inclure le jeton CSRF
                },
                success: function(response) {
                    loader.classList.add('d-none');
                    if (response.success) {
                        alert(response.message);
                        location.reload();
    
                        var missionContainer = selectElement.closest('.accordion-body');
                        var missionContainer2 = selectElement.closest('.assignee2');
                        console.log(missionContainer2)
                        
                        // Supprimer le select après l'assignation
                        selectElement.parentNode.remove();
    
                        // Insertion dans le conteneur de la mission (détails complets)
                        var moderateurInfo = `
                            <div class="d-flex flex-row justify-content-start mt-3">
                                <div class="me-4">
                                    <h5>${moderateurName}</h5> 
                                    <p>Modérateur Assigné</p>
                                </div>
                                <div>
                                    <h5>${response.status}</h5> 
                                    <p>Status</p>
                                </div>
                            </div>
    
                            <div class="d-flex flex-column mt-3">
                                <div class="d-flex">
                                    <button type="button" class="btn btn-success me-3" data-bs-toggle="modal" data-bs-target="#accepter" onclick='handleMissionAction("accepter", ${JSON.stringify(response.mission)})'>Rédiger le mail</button>
    
                                    <form action="{{ route('change.etat.mission') }}" method="POST" id="changeEtatMissionForm">
                                        @csrf
                                        <input type="hidden" name="etat" value="refuser" id="etatMission">
                                        <input type="hidden" name="id_mission" id="idMission" value="${response.mission.id}">
                                        <button type="submit" class="btn btn-danger me-3">Refuser</button>
                                    </form>
    
                                    <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#refuser" onclick='handleMissionEtat("ajouter_crm", ${JSON.stringify(response.mission)})'>Compte CRM</button>
                                </div>
                            </div>
                        `;
    
                        // Insertion dans le conteneur "aprc-wrapper" (détails simplifiés)
                        var moderateurInfo2 = `
                            <div class="traitee">
                                <p>assigné à</p>
                                <h5>${moderateurName}</h5> 
                            </div>
                        `;
    
                        // Ajouter les informations du modérateur dans les deux conteneurs
                        // missionContainer.insertAdjacentHTML('beforeend', moderateurInfo);
                        missionContainer2.insertAdjacentHTML('beforeend', moderateurInfo2);
                        console.log(missionContainer2)
    
                    }
                },
                error: function(xhr, status, error) {
                    loader.classList.add('d-none');
                    alert('Une erreur est survenue. Veuillez réessayer.');
                    console.error(xhr.responseText);
                }
            });
        } else {
            alert('Veuillez choisir un modérateur');
        }
    }
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    function assignMissionrefuser(missionId, selectElement) {
        // Récupérer l'ID du modérateur sélectionné
        var moderateurId = selectElement.value;
        var moderateurName = selectElement.options[selectElement.selectedIndex].text;
        var loader = document.getElementById('loading-' + missionId);
        
        if (moderateurId) {
            loader.classList.remove('d-none');
            
            $.ajax({
                url: '{{ route("assign.moderator.refuser.candidats") }}',
                type: 'POST',
                data: {
                    mission_id: missionId,
                    moderateur_id: moderateurId,
                    _token: '{{ csrf_token() }}' // Inclure le jeton CSRF
                },
                success: function(response) {
                    loader.classList.add('d-none');
                    if (response.success) {
                        alert(response.message);
    
                        var missionContainer2 = selectElement.closest('.assignee2');
                        console.log(missionContainer2)
                        var mission_refuser = document.getElementById('mission_refuser');
    
                        // Supprimer le select après l'assignation
                        selectElement.parentNode.remove();
                        mission_refuser.remove();
                        location.reload();
    
                       
    
                        // Insertion dans le conteneur "aprc-wrapper" (détails simplifiés)
                        var moderateurInfo2 = `
                            <div class="traitee">
                                <p>Assigné à</p>
                                <h5>${moderateurName}</h5> 
                            </div>
                        `;
    
                        // Ajouter les informations du modérateur dans les deux conteneurs
                        // missionContainer.insertAdjacentHTML('beforeend', moderateurInfo);
                        missionContainer2.insertAdjacentHTML('beforeend', moderateurInfo2);
                        console.log(missionContainer2)
    
                    }
                },
                error: function(xhr, status, error) {
                    loader.classList.add('d-none');
                    alert('Une erreur est survenue. Veuillez réessayer.');
                    console.error(xhr.responseText);
                }
            });
        } else {
            alert('Veuillez choisir un modérateur');
        }
    }
    
    
    function assignMissionAutre(missionId, selectElement) {
        // Récupérer l'ID du modérateur sélectionné
        var moderateurId = selectElement.value;
        var moderateurName = selectElement.options[selectElement.selectedIndex].text;
        var loader = document.getElementById('loading-' + missionId);
        
        if (moderateurId) {
            loader.classList.remove('d-none');
            
            $.ajax({
                url: '{{ route("assign.moderator.refuser.candidats") }}',
                type: 'POST',
                data: {
                    mission_id: missionId,
                    moderateur_id: moderateurId,
                    _token: '{{ csrf_token() }}' // Inclure le jeton CSRF
                },
                success: function(response) {
                    loader.classList.add('d-none');
                    if (response.success) {
                        alert(response.message);
                        location.reload();
    
                        var missionContainer2 = selectElement.closest('.assignee2');
                        console.log(missionContainer2)
                        var mission_refuser = document.getElementById('mission_refuser');
    
                      
                        console.log(missionContainer2)
    
                    }
                },
                error: function(xhr, status, error) {
                    loader.classList.add('d-none');
                    alert('Une erreur est survenue. Veuillez réessayer.');
                    console.error(xhr.responseText);
                }
            });
        } else {
            alert('Veuillez choisir un modérateur');
        }
    }
    












    function prepareRefusModal(missionId) {
        document.getElementById('refus_mission_id').value = missionId;
        const motifTextarea = document.getElementById('motif_refus_input');
        motifTextarea.value = ''; // Clear previous motif
        motifTextarea.classList.remove('is-invalid'); // Reset validation state
    }

    document.addEventListener('DOMContentLoaded', function() {
        const refusForm = document.getElementById('refusMissionForm');
        if (refusForm) {
            refusForm.addEventListener('submit', function(event) {
                const motifTextarea = document.getElementById('motif_refus_input');
                if (!motifTextarea.value.trim()) {
                    event.preventDefault(); // Prevent form submission
                    motifTextarea.classList.add('is-invalid');
                } else {
                    motifTextarea.classList.remove('is-invalid');
                }
            });
        }
    });

    
    </script>
    